import api from '@/api'
import { Get, Post } from '@/common/request'

function getBasicInfo(param) {
    return Get({
        // url: api.getBasicInfoH5,
        url: api.getBasicInfoH5,
        urlParam: param
    })
}
function getRealData(param) {
    return Get({
        url: api.getRealDataH5,
        urlParam: param
    })
}
function getRealCells(param) {
    return Get({
        url: api.getRealCellsH5,
        urlParam: param
    })
}
function getPowerBmsChargeRecordPageList(obj,params) {
    return Post({
        url: api.getPowerBmsChargeRecordPageListH5,
        bodyParam: obj,
        urlParam: params
    })
}
function statsPowerBattDurUsageSummary(obj,params) {
    return Post({
        url: api.statsPowerBattDurUsageSummaryH5,
        bodyParam: obj,
        urlParam: params
    })
}
function powerDeviceAlarmPage(obj) {
    return Post({
        url: api.powerDeviceAlarmPageH5,
        bodyParam: obj
    })
}
function statsPowerBattDailyUsage(obj,params) {
    return Post({
        url: api.statsPowerBattDailyUsageH5,
        bodyParam: obj,
        urlParam: params
    })
}

export default {
    getBasicInfo,
    getRealData,
    getRealCells,
    getPowerBmsChargeRecordPageList,
    statsPowerBattDurUsageSummary,
    powerDeviceAlarmPage,
    statsPowerBattDailyUsage,
}