.el-button {
  font-weight: normal !important;
  color: var(--text-80) !important;
}

.el-button+.el-button {
  margin-left: 0 !important;
}

.el-popconfirm__action {
  .el-button+.el-button {
    margin-left: 12px !important;
  }

  .el-button:first-child {
    border: 1px solid #d9d9d9;
  }

  .el-button--primary {
    background-color: transparent;
    color: var(--themeColor);
  }
}

.el-popconfirm__main {
  align-items: start !important;
  line-height: 1.4em;
  .el-icon{
    margin-top: 0.2em;
  }
}

.el-popper.is-light {
  max-width: 600px !important;
}

.el-popper.is-light.el-picker__popper{
  max-width: initial !important;
}
.el-table {
  color: var(--text-80) !important;
  background-color: transparent !important;
}

.el-table thead.is-group th.el-table__cell {
  background: var(--header-bg) !important;
}

.el-table tr {
  background: transparent !important;
}

.el-table thead th {
  font-weight: normal !important;
  color: var(--text-60) !important;
}

.el-drawer__body {
  padding-top: 16px !important;
}

.el-drawer__header {
  margin-bottom: 0 !important;
  padding: 11px 20px !important;
  border-bottom: 1px solid #d9d9d9;
  color: #000;
  font-size: 16px;
  font-weight: 500;
  color: rgba(34, 34, 34, 1);
}

.ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-link-icon,
.ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-link-icon {
  color: var(--themeColor);
}

.el-tabs__nav{
  padding: 0px !important;
  border-radius: 8px !important;
  border: 0px solid transparent !important;
  column-gap: 16px !important;
}
.el-tabs__nav-wrap:after {
  display: none !important;
}
.el-tabs__item {
  // color: rgba(34, 34, 34, 1) !important;
  // font-weight: normal !important;
  color: var(--text-100) !important;
  font-weight: normal;
  line-height: 36px;
  height: 36px;
  padding: 0 !important;
  &:hover{
    color: var(--themeColor) !important;
  }
}
.el-tabs__item.is-active {
  border-radius: 8px !important;
  text-align: center !important;
  font-weight: 500 !important;
  color: var(--themeColor) !important;
  &::after {
    content: '';
    display: block;
    position: absolute;
    bottom: 0;
    left: 50% !important;
    margin-left: -18px !important;
    width: 36px !important;
    height: 4px !important;
    background: var(--themeColor) !important;
}
}

.el-tabs__active-bar{
  display: none !important;
  background-color: var(--themeColor) !important;
}

// 页码
.el-pager {
  column-gap: 8px;
}

.el-pager li {
  &:hover {
    border: 1px solid var(--themeColor);
  }
}

.el-pager li.is-active {
  border: 1px solid var(--themeColor);
  font-weight: normal !important;
}

.el-pagination {
  column-gap: 8px;

  button {
    &:hover {
      border: 1px solid var(--themeColor);
    }
  }
}
.el-input{
  color: var(--input-color) !important;
}
.el-input-group__append,
.el-input-group__prepend {
  background-color: #fff !important;
}

.el-input__inner {
  color: var(--input-color) !important;
}
.el-textarea__inner{
  color: var(--input-color) !important;
}

// 按钮primary默认背景
.el-button.is-text {
  background-color: #fff !important;
}

.el-button--primary.is-plain {
  background: none;
}

.el-button:hover {
  color: #fff !important;
  background-color: var(--themeColor) !important;
}

.el-button.is-disabled:hover {
  background-color: var(--el-button-disabled-bg-color) !important;
  color: var(--el-button-disabled-text-color) !important;

}

.el-button--primary.is-plain.is-disabled:hover {
  color: var(--el-color-primary-light-5) !important;

}

.el-button--primary.is-link.is-disabled,
.el-button--primary.is-link.is-disabled:active,
.el-button--primary.is-link.is-disabled:focus,
.el-button--primary.is-link.is-disabled:hover,
.el-button--primary.is-plain.is-disabled,
.el-button--primary.is-plain.is-disabled:active,
.el-button--primary.is-plain.is-disabled:focus,
.el-button--primary.is-plain.is-disabled:hover,
.el-button--primary.is-text.is-disabled,
.el-button--primary.is-text.is-disabled:active,
.el-button--primary.is-text.is-disabled:focus,
.el-button--primary.is-text.is-disabled:hover {
  background: transparent !important;
}

.el-table-v2__header-cell{
  font-weight: normal !important;
  background: var(--header-bg) !important;
  color: var(--text-60) !important;
  user-select: auto !important;
}
.el-table-fixed-column--left{
 
}
.el-table__body-wrapper tr td.el-table-fixed-column--left, .el-table__body-wrapper tr td.el-table-fixed-column--right, .el-table__body-wrapper tr th.el-table-fixed-column--left, .el-table__body-wrapper tr th.el-table-fixed-column--right, .el-table__footer-wrapper tr td.el-table-fixed-column--left, .el-table__footer-wrapper tr td.el-table-fixed-column--right, .el-table__footer-wrapper tr th.el-table-fixed-column--left, .el-table__footer-wrapper tr th.el-table-fixed-column--right, .el-table__header-wrapper tr td.el-table-fixed-column--left, .el-table__header-wrapper tr td.el-table-fixed-column--right, .el-table__header-wrapper tr th.el-table-fixed-column--left, .el-table__header-wrapper tr th.el-table-fixed-column--right{
  background: #fff !important;
}
.dark {

  // 按钮primary默认背景
  .el-button.is-text {
    background-color: transparent !important;
  }

  .el-button--primary.is-plain {
    background: none;
  }

  .el-button {
    color: var(--themeColor) !important;
    background-color: transparent !important;
    border-color: var(--themeColor) !important;
  }
.el-button[white] {
  color: #fff !important;
  background-color: transparent !important;
  border-color: #fff !important;
}
  .el-button:hover {
    color: #fff !important;
    background-color: var(--themeColor) !important;
    border-color: var(--themeColor) !important;
  }

  .el-button.is-disabled:hover {
    background-color: var(--el-button-disabled-bg-color) !important;
    color: var(--el-button-disabled-text-color) !important;
  }

  .el-button--primary.is-plain.is-disabled:hover {
    color: var(--el-color-primary-light-5) !important;
  }
}

.dialog-footer {
  column-gap: 12px;
}

.dark .el-drawer {
  background-color: var(--input-bg);
  backdrop-filter: blur(10px);
}

.el-tree {
  background: transparent !important;
}

.el-input__inner::placeholder {
  color: var(--placeholder) !important;
}

.el-input__wrapper {
  background-color: var(--input-bg) !important;
  color: var(--input-color) !important;
}

.el-date-editor .el-range-input {
  color: var(--input-color) !important;
}

.el-select__wrapper {
  color: var(--input-color) !important;
  background-color: var(--input-bg) !important;
}

.el-select__input {
  color: var(--input-color) !important;
}

.el-select__selected-item{
  color: var(--input-color) !important;
}

.el-select__placeholder.is-transparent {
  color: var(--placeholder) !important;
}
.el-form-item__label {
  color: var(--text-60) !important;
}
.el-table-v2 {
  color: var(--text-80) !important;
  background-color: transparent !important;
}

.el-table-v2__row {
  background: transparent !important;
}

.el-table-v2__cell {
  color: var(--text-80) !important;
}

.el-table-v2__header-row {
  background: var(--header-bg) !important;
}

.el-table-v2__fixed-left,
.el-table-v2__fixed-right {
  background: #fff !important;
}

// .el-input+.el-button{
//   border-top-left-radius: 0;
//   border-bottom-left-radius: 0;
// }
.dialog-footer{
  display: flex;
  justify-content: flex-end;
}
.el-table-v2__row-cell{
  padding-top:12px !important;
  padding-bottom:12px !important;
}
.el-table-v2__row{
  border-bottom:1px solid var(--border) !important
}

.filter-options {
  padding:12px 12px 0px 12px;
  .el-checkbox {
      display: block;
      margin-right: 0;
      // margin-bottom: 8px;
      &:last-child {
          margin-bottom: 0;
      }
  }
  .el-button {
      border-color: var(--themeColor);
  }
}
.el-select__wrapper.is-disabled{
  // opacity: 0.6 !important;
  cursor: not-allowed !important;

  
}
.el-select__wrapper.is-disabled .el-select__selected-item{
  color: var(--input-color) !important;
  opacity: 0.5;
}
.el-input .el-input__icon{
  font-size: 20px !important;
  color: var(--text-80) !important;
}
.el-date-editor .el-range__icon{
  font-size: 20px !important;
  color: var(--text-80) !important;
}
.dark {
  .el-popper {
    border-color: transparent !important;
  }

  .el-popper.is-light {
    background: var(--main-bg) !important;
  }


  .el-select-dropdown__item {
    color: var(--text-100) !important;
  }

  .el-select-dropdown__item.is-selected {
    color: var(--themeColor) !important;
    background-color: var(--selected-color) !important;
  }

  .el-select-dropdown__item.is-hovering {
    color: var(--themeColor) !important;
    background-color: var(--selected-color) !important;
  }

  .el-popconfirm__main {
    color: var(--text-80);
  }

  .el-picker-panel {
    background: var(--main-bg) !important;
    border: var(--border) !important;
  }

  .el-date-picker__header {
    color: var(--input-color) !important;
  }

  .el-picker-panel__icon-btn {
    color: var(--input-color) !important;
  }

  .el-date-picker__header-label {
    color: var(--input-color) !important;
  }

  .el-date-table th {
    color: var(--input-color) !important;
  }

  .el-date-table td .el-date-table-cell .el-date-table-cell__text {
    color: var(--text-100) !important;
  }

  .el-date-table td.current:not(.disabled) .el-date-table-cell__text {
    color: var(--themeColor) !important;
  }

  .el-date-table td.prev-month .el-date-table-cell .el-date-table-cell__text {
    color: var(--text-40) !important;
  }

  .el-date-table td.next-month .el-date-table-cell .el-date-table-cell__text {
    color: var(--text-40) !important;
  }

  .el-date-table td.in-range .el-date-table-cell {
    color: var(--themeColor) !important;
    background-color: #05345F;
  }

  .el-date-table td.in-range .el-date-table-cell .el-date-table-cell__text {
    color: var(--themeColor) !important;
    // opacity: 0.6;
  }

  .el-date-table td.in-range .el-date-table-cell:hover {
    background-color: #05345F;
  }

  .el-date-table td.next-month.disabled .el-date-table-cell .el-date-table-cell__text {
    color: var(--text-100) !important;
    opacity: 0.6;
  }

  .el-date-table td.normal.disabled .el-date-table-cell .el-date-table-cell__text {
    color: var(--text-100) !important;
    opacity: 0.6;
  }

  .el-date-table td.start-date .el-date-table-cell .el-date-table-cell__text {
    color: var(--text-100) !important;
  }
  .el-date-table td.end-date .el-date-table-cell__text, .el-date-table td.start-date .el-date-table-cell__text{
    background-color: var(--themeColor);
  }
  .el-date-table td.end-date .el-date-table-cell .el-date-table-cell__text {
    color: var(--text-100) !important;
    opacity: 1;
  }
  .el-date-table td.current:not(.disabled) .el-date-table-cell__text{
    background-color: var(--themeColor);
    color: var(--text-100) !important
  }

  .el-date-table td.disabled .el-date-table-cell {
    background-color: transparent !important;
    opacity: 0.6;
  }

  .el-date-range-picker__header {
    color: var(--text-100) !important;
  }




  .el-month-picker__header {
    color: var(--input-color) !important;
  }

  .el-picker-panel__icon-btn {
    color: var(--input-color) !important;
  }

  .el-month-picker__header-label {
    color: var(--input-color) !important;
  }

  .el-month-table th {
    color: var(--input-color) !important;
  }

  .el-month-table td .el-date-table-cell .el-date-table-cell__text {
    color: var(--text-100) !important;
  }

  .el-month-table td.current:not(.disabled) .el-date-table-cell__text {

  }

  .el-month-table td.prev-month .el-date-table-cell .el-date-table-cell__text {
    color: var(--text-40) !important;
  }

  .el-month-table td.next-month .el-date-table-cell .el-date-table-cell__text {
    color: var(--text-40) !important;
  }

  .el-month-table td.in-range .el-date-table-cell {
    color: var(--themeColor) !important;
    background-color: #05345F;
  }

  .el-month-table td.in-range .el-date-table-cell .el-date-table-cell__text {
    color: var(--themeColor) !important;
    opacity: 0.6;
  }

  .el-month-table td.in-range .el-date-table-cell:hover {
    background-color: #05345F;
  }

  .el-month-table td.next-month.disabled .el-date-table-cell .el-date-table-cell__text {
    color: var(--text-100) !important;
    opacity: 0.6;
  }

  .el-month-table td.normal.disabled .el-date-table-cell .el-date-table-cell__text {
    color: var(--text-100) !important;
    opacity: 0.6;
  }

  .el-month-table td.start-date .el-date-table-cell .el-date-table-cell__text {
    color: var(--text-100) !important;
  }
  .el-month-table td.end-date .el-date-table-cell__text, .el-month-table td.start-date .el-date-table-cell__text{
    background-color: var(--themeColor);
  }
  .el-month-table td.end-date .el-date-table-cell .el-date-table-cell__text {
    color: var(--text-100) !important;
    opacity: 1;
  }
  .el-month-table td.current:not(.disabled) .el-date-table-cell__text{
    background-color: var(--themeColor);
    color: var(--text-100) !important
  }


  .el-month-table td .el-date-table-cell__text {
    color: var(--text-100) !important;
  }

  .el-month-table td.disabled .el-date-table-cell__text{
    background-color: transparent !important;
  }
  .el-month-table td.disabled .el-date-table-cell {
    background-color: transparent !important;
    opacity: 0.6;
  }

  .el-month-range-picker__header {
    color: var(--text-100) !important;
  }



  .el-popper.is-light,
  .el-popper.is-light>.el-popper__arrow:before {
    background: var(--main-bg);
    border: var(--main-bg);
  }

  .el-table th.el-table__cell {
    background-color: var(--header-bg) !important;
  }

  .el-pagination.is-background .btn-next.is-disabled,
  .el-pagination.is-background .btn-next:disabled,
  .el-pagination.is-background .btn-prev.is-disabled,
  .el-pagination.is-background .btn-prev:disabled,
  .el-pagination.is-background .el-pager li.is-disabled,
  .el-pagination.is-background .el-pager li:disabled {
    background: transparent;
  }

  .el-pagination.is-background .btn-next.is-active,
  .el-pagination.is-background .btn-prev.is-active,
  .el-pagination.is-background .el-pager li.is-active {
    background-color: transparent;
    color: var(--themeColor);
  }

  .el-pagination.is-background .btn-next,
  .el-pagination.is-background .btn-prev,
  .el-pagination.is-background .el-pager li {
    background-color: transparent;
    color: var(--text-100);
  }

  .el-pager li.is-active,
  .el-pager li:hover {
    color: var(--themeColor);
  }

  .el-pagination.is-background .el-pager li.is-active,
  .el-pagination.is-background .el-pager li:hover {
    color: var(--themeColor);
  }

  .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
    background-color: var(--table-hover-bg);
  }

  .el-loading-mask {
    background-color: var(--border) !important;
  }

  .drawer-header {
    color: var(--text-100);
  }

  .el-textarea.is-disabled .el-textarea__inner {
    background: transparent;
    color: var(--text-100);
  }

  .el-textarea__inner {
    background-color: transparent;
    color: var(--text-100);
  }

  .el-table td.el-table__cell,
  .el-table th.el-table__cell.is-leaf {

    border-color: var(--border);
  }

  .el-table--border .el-table__cell {
    border-color: var(--border);
  }

  .el-table--border .el-table__inner-wrapper:after,
  .el-table--border:after,
  .el-table--border:before,
  .el-table__inner-wrapper:before {
    background-color: var(--border);
  }

  .el-table__border-bottom-patch,
  .el-table__border-left-patch {
    background-color: var(--border);
  }

  .el-table__body tr.hover-row.current-row>td.el-table__cell,
  .el-table__body tr.hover-row.el-table__row--striped.current-row>td.el-table__cell,
  .el-table__body tr.hover-row.el-table__row--striped>td.el-table__cell,
  .el-table__body tr.hover-row>td.el-table__cell,
  .el-table__body tr>td.hover-cell {
    background-color: var(--table-hover-bg);
  }

  .el-divider--vertical {
    border-color: var(--border);
  }

  .el-date-editor .el-range-separator {
    color: var(--text-100);
  }

  .el-select__wrapper {
    background-color: transparent !important;
  }

  .el-input__wrapper {
    background-color: transparent !important;
  }
  .el-date-editor .el-range__icon{
    color: var(--text-100);
  }
  .el-input .el-input__icon{
    color: var(--text-100);
  }
  .el-checkbox__inner{
    background-color: transparent !important;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner{

    background-color: transparent !important;
  }

  .el-checkbox__label{
    color: var(--text-100);
  }
  .el-input.is-disabled{
    opacity: 0.4;
  }
  .el-select.is-disabled{
    opacity: 0.4;
  }
  .dark .el-switch__core .el-switch__action{

  }
  .el-switch.is-checked .el-switch__core .el-switch__action{
    // background-color: var(--themeColor);
    // color: var(--themeColor);
  }
  .el-switch.is-checked .el-switch__core{
    background-color: var(--themeColor);
  }
  .el-button.is-disabled, .el-button.is-disabled:hover{
    opacity: 0.4;
  }

          .el-table__header-wrapper tr td.el-table-fixed-column--left,
           .el-table__header-wrapper tr td.el-table-fixed-column--right,
            .el-table__header-wrapper tr th.el-table-fixed-column--left,
             .el-table__header-wrapper tr th.el-table-fixed-column--right{
    background:var(--header-bg) !important;
    backdrop-filter: blur(10px);
  }
  .el-table__body-wrapper tr td.el-table-fixed-column--left,
  .el-table__body-wrapper tr td.el-table-fixed-column--right,
   .el-table__body-wrapper tr th.el-table-fixed-column--left,
    .el-table__body-wrapper tr th.el-table-fixed-column--right,
     .el-table__footer-wrapper tr td.el-table-fixed-column--left,
      .el-table__footer-wrapper tr td.el-table-fixed-column--right,
       .el-table__footer-wrapper tr th.el-table-fixed-column--left,
        .el-table__footer-wrapper tr th.el-table-fixed-column--right{
          background:transparent !important;
          backdrop-filter: blur(10px);
        }
        .el-table-v2__main{
          background-color: transparent !important;
        }
        .el-radio-button__inner{
          background-color: transparent;
          color:#fff
        }
        .el-radio-button.is-active .el-radio-button__original-radio:not(:disabled)+.el-radio-button__inner{
          background-color: var(--themeColor) ;
        }
      
        .el-table-v2 {
          color: var(--text-80) !important;
          background-color: transparent !important;
        }
      
        .el-table-v2__row:hover {
          background-color: var(--table-hover-bg) !important;
        }
      
        .el-table-v2__cell {
          border-color: var(--border) !important;
        }
      
        .el-table-v2__fixed-left,
        .el-table-v2__fixed-right {
          background: transparent !important;
          backdrop-filter: blur(10px);
        }
      
        .el-table-v2__header-cell {
          font-weight: normal !important;
          color: var(--text-60) !important;
          // background: var(--header-bg) !important;
          background: transparent !important;
          backdrop-filter: blur(10px);
        }
        .el-table-v2__header-wrapper{
          background-color: var(--header-bg) !important;
        }
        .el-table-v2__left{
          background-color: transparent !important;
          backdrop-filter: blur(10px);
          .el-table-v2__header-wrapper{
            background-color: transparent !important;
          }
        }
        .el-table-v2__right{
          background-color: transparent !important;
          backdrop-filter: blur(10px);
          .el-table-v2__header-wrapper{
            background-color: transparent !important;
          }
        }
        // .el-table-v2__header-row .el-table-v2__dynamic-header-row{
        //   padding-right: 0 !important;
        // }
        .el-table-v2__header-row, .el-table-v2__row{
          padding-inline-end: 0 !important;
        }
        .el-table-v2__header-row{
          background: transparent !important;
        }
       
        .el-tag{
          background-color: var(--bg-f5);
          color: var(--input-color);
        }
        .el-input-group__append, .el-input-group__prepend{
          background-color: transparent !important;

        }
        .el-input-group__append{
          button.el-button{
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-color:#fff !important;
            border-left:0 !important;
            .el-icon{
              fill:#fff !important;
              color:#fff !important;
            }
          }
        }
        .el-input-group__prepend{
          button.el-button{
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            border-color:#fff !important;
            border-right:0 !important;
            .el-icon{
              fill:#fff !important;
              color:#fff !important;
            }
          }
        }
  .el-dialog {
    background: var(--main-bg) !important;
    border: 1px solid var(--border) !important;

    .el-dialog__header {
      border-bottom: 1px solid var(--border) !important;
      padding: 20px;
      margin: 0;

      .el-dialog__title {
        color: var(--text-100) !important;
        font-size: 16px;
        font-weight: 500;
      }

      .el-dialog__headerbtn {
        .el-dialog__close {
          color: var(--text-60) !important;
        }
      }
    }

    .el-dialog__body {
      color: var(--text-100) !important;
      padding: 20px;
    }

    .el-dialog__footer {
      border-top: 1px solid var(--border) !important;
      padding: 20px;
      margin: 0;
    }
  }

  .el-picker-panel [slot=sidebar], .el-picker-panel__sidebar{
    background: var(--main-bg) !important;
  }
  .el-picker-panel__shortcut{
    color: var(--text-100) !important;
    &:hover{
      color:var(--themeColor)
    }
  }
  .el-radio__input.is-checked .el-radio__inner{
    background:var(--themeColor) !important
  }
  .el-radio{
    color: var(--text-80);
  }
  .el-tree-node:focus > .el-tree-node__content{
    background-color: rgba(255, 255, 255, 0.02) !important;
}
  .el-tree-node__content{
    &:hover {
      background-color: rgba(255, 255, 255, 0.02) !important;
  }
  }
  .el-tree-node__label{
    color: var(--text-100) !important;
  }
  .el-input-number__decrease, .el-input-number__increase{
    background-color: transparent !important;
  }
}
