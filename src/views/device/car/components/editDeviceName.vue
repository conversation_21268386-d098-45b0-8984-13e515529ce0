<template>
    <el-drawer
        :model-value="visible"
        width="500px"
        @close="onClose"
        :show-close="false"
    >
        <template #header>
            <div
                class="drawer-header flex items-center justify-between leading-5.5"
            >
                <div class="drawer-header text-primary-text dark:text-80-dark">
                    <span>{{ $t('Device Naming') }}</span>
                </div>
                <div class="flex gap-x-3 items-center">
                    <el-button @click="onClose" round>{{
                        $t('Cancle')
                    }}</el-button>
                    <el-button @click="handleSave" type="primary" round>{{
                        $t('Save')
                    }}</el-button>
                </div>
            </div>
        </template>
        <div>
            <el-form v-loading="loading" :model="form" ref="formRef">
                <div
                    v-for="(item, index) in form.deviceList"
                    :key="item.batteryNo"
                >
                    <el-form-item
                        :label="$t('Device No')"
                        style="margin-bottom: 2px"
                    >
                        <span>{{ item.batteryNo }}</span>
                    </el-form-item>
                    <el-form-item
                        label=""
                        :prop="'deviceList.' + index + '.remarkName'"
                        :rules="[
                            {
                                required: true,
                                message: $t('Please Input Vehicle Name'),
                                trigger: 'blur',
                            },
                            {
                                max: 10,
                                message: $t('Maximum length: 10'),
                                trigger: 'blur',
                            },
                        ]"
                        style="margin-bottom: 12px"
                    >
                        <el-input
                            v-model="item.remarkName"
                            :maxlength="10"
                        ></el-input>
                    </el-form-item>
                </div>
            </el-form>
        </div>
    </el-drawer>
</template>

<script>
import { defineComponent, ref, watch, reactive } from 'vue'
import powerApi from '@/apiService/power'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'

export default defineComponent({
    name: 'EditDeviceName',
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        supplierId: {
            type: String,
            default: '',
        },
    },
    emits: ['update:visible', 'update'],
    setup(props, { emit }) {
        const { t } = useI18n()
        const loading = ref(false)
        const formRef = ref(null)
        const form = reactive({
            deviceList: [],
        })

        const getDeviceList = async () => {
            loading.value = true
            try {
                // const res = await powerApi.getSimplePageList({
                const res = await powerApi.getDevicePageList({
                    size: 99999,
                    current: 1,
                    supplierId: props.supplierId,
                })
                if (res.data.code === 0) {
                    form.deviceList = res.data.data.records
                }
            } catch (error) {
                console.error('Failed to get device list:', error)
            } finally {
                loading.value = false
            }
        }

        watch(
            () => props.visible,
            (newVal) => {
                if (newVal) {
                    getDeviceList()
                }
            }
        )

        const onClose = () => {
            emit('update:visible', false)
        }

        const handleSave = () => {
            formRef.value.validate(async (valid) => {
                if (valid) {
                    loading.value = true
                    const params = form.deviceList.map((item) => ({
                        remarkName: item.remarkName,
                        bmsId: item.id,
                    }))
                    try {
                        const res = await powerApi.batchRemarkName({
                            remarkList: params,
                        })
                        if (res.data.code === 0) {
                            ElMessage.success(t('Successed'))
                            emit('update')
                            onClose()
                        }
                    } catch (error) {
                        console.error('Failed to save device names:', error)
                    } finally {
                        loading.value = false
                    }
                }
            })
        }

        return {
            t,
            loading,
            formRef,
            form,
            onClose,
            handleSave,
        }
    },
})
</script>
