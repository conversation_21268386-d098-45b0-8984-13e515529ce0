<template>
    <div>
        <div class="text-title dark:text-title-dark leading-9 mb-1.5">
            {{ $t('Revenue rank') }}
        </div>
        <div class="rank">
            <div
                class="rank-header bg-ff rounded text-title dark:text-title-dark flex justify-between items-center text-center py-3 mb-2.5"
            >
                <div class="rank-num h-5.5">
                    {{ $t('Rank') }}
                </div>
                <div class="rank-name flex-1">
                    {{ $t('Station') }}
                </div>
                <div class="flex items-center justify-start rank-process">
                    <div>
                        {{ $t('station_shouyi')
                        }}<template v-if="locale == 'zh'"
                            >({{ profitUnit }})</template
                        >
                    </div>
                </div>
            </div>
            <div class="space-y-4">
                <template v-for="(item, index) in rankList" :key="index">
                    <div
                        class="flex items-center text-title dark:text-title-dark"
                        v-if="index < 5"
                    >
                        <div class="h-5.5 rank-num">
                            {{ index + 1 }}
                        </div>
                        <div
                            class="rank-name overflow flex-1"
                            :title="item.stationName"
                        >
                            {{ item.stationName }}
                        </div>
                        <div
                            class="flex justify-start items-center rank-process"
                        >
                            <div class="leading-5.5 w-18 quantity text-center">
                                {{ item.profit }}
                            </div>
                            <BarProgress :fullWidth="80" :ratio="item.ratio" />
                        </div>
                    </div>
                </template>
                <template v-if="!rankList?.length">
                    <empty-data
                        :description="$t('zanwushuju')"
                        class="mx-auto mt-10 text-secondar-text dark:text-60-darl"
                    >
                        <slot name="empty"></slot>
                    </empty-data>
                </template>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import BarProgress from '../components/barProgress.vue'
const { t, locale } = useI18n()
const props = defineProps({
    rankList: {
        type: Array,
        default: () => [],
    },
    type: {
        type: String,
        default: () => 'a',
    },
    profitUnit: {
        type: String,
        default: () => '元',
    },
})
</script>

<style lang="less" scoped>
.rank {
    width: 440px;
    height: 278px;
    // background: var(--bg-f5);
    border-radius: 6px;
    padding: 4px 6px;
    border: 1px solid var(--border);
    background: var(--bg-white);
    .rank-num {
        width: 56px;
        text-align: center;
        margin-right: 4px;
    }

    .rank-name {
        width: 164px;
        text-align: left;
        padding-right: 4px;
    }

    .rank-process {
        width: 160px;

        .quantity {
            width: 80px;
            text-align: left;
        }
    }

    .toggle {
        width: 16px;
        height: 16px;
    }
}
</style>
