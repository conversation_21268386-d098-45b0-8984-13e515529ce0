
const config = {
    orgLogo: require('@/assets/login/logox1.png'),
    loginBanner: require('@/assets/login/login-bg.jpg'),
    themeColor: '#6FBECE',
    webPageIcon: require('@/assets/login/icon.png'),
    webPageTitle: '上善能及能源云',
}

function setColor(data) {
    const obj = {}    
    Object.keys(data).forEach(key => {
        obj[key] = data[key] ? data[key] : config[key]
    })
    return obj
}

import { setThemeColor } from '@/common/util.js'

const setDeflutColor = () => {
    document.getElementById('title').innerHTML =  config.webPageTitle;
    document.getElementById('icon-svg').href = config.webPageIcon;
    document.getElementsByTagName('body')[0].style.setProperty('--themeColor', config.themeColor);
    setThemeColor(config.themeColor)
}

export default { config, setDeflutColor, setColor }