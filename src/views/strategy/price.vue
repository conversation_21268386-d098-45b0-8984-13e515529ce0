<template>
    <div>电价管理</div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const goRouter = () => {
    router.go(-1)
}
const tabActiveKey = ref('energy')
</script>

<style lang="less" scoped>
.go-box {
    color: #222222;

    .bt-box-go {
        display: inline-block;
        width: 32px;
        height: 32px;
        line-height: 32px;
        background-color: #fff;
        text-align: center;

        &:hover {
            background-color: var(--themeColor);
            color: #fff;
        }
    }
}

.blockTabs {
    background-color: #fff;
    box-sizing: border-box;
    padding-top: 6px;

    :deep(.ant-tabs-bar) {
        border-bottom: 0;
        margin-bottom: 16px;
    }

    :deep(.ant-tabs-content) {
        flex: 1;
    }

    :deep(.ant-tabs-tab) {
        margin: 0 8px 0 0;
        padding: 16px 0;
        margin: 0 16px;
        padding-bottom: 8px;
        font-size: 16px;

        &:hover {
            color: var(--themeColor);
        }
    }

    :deep(.ant-tabs-nav) {
        width: 100%;
    }

    :deep(.ant-tabs-nav .ant-tabs-tab-active) {
        color: var(--themeColor);
    }

    :deep(.ant-tabs-ink-bar) {
        background: var(--themeColor);
        width: 64px;
    }

    .twoHeight {
        height: 358px;
        padding: 0 16px 16px 16px;
    }
}
</style>
