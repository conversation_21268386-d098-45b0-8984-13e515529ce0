<template>
    <el-drawer
        :model-value="visible"
        :size="486"
        :show-close="false"
        @close="onClose"
        wrapClassName="drawerBox"
    >
        <template #header>
            <div
                class="drawer-header flex items-center justify-between leading-5.5"
            >
                <div class="drawer-header text-primary-text dark:text-80-dark">
                    <span>{{ '电价模版详情' }}</span>
                </div>
                <div class="flex gap-x-3 items-center">
                    <el-button plain round @click="onClose">{{
                        $t('common_guanbi')
                    }}</el-button>
                    <el-popconfirm
                        :title="'是否确认删除？'"
                        @confirm="onDelete"
                        confirm-button-text="是"
                        cancel-button-text="否"
                    >
                        <template #reference>
                            <el-button plain round class="btn-hover">
                                <span>删除</span>
                            </el-button>
                        </template>
                    </el-popconfirm>
                    <el-button
                        plain
                        round
                        type="primary"
                        :disabled="detail.beAuthorized == 1"
                        @click="onEmit"
                        >编辑</el-button
                    >
                </div>
            </div>
        </template>
        <div>
            <div class="flex flex-col text-title dark:text-title-dark">
                <div class="py-1">
                    <span
                        class="title-l title-content text-secondar-text dark:text-60-dark"
                        >模版名称：</span
                    >
                    <span class="title-r col">{{ detail?.name }}</span>
                </div>
                <div class="py-1 alarm-stauts">
                    <span
                        class="title-l title-content text-secondar-text dark:text-60-dark"
                        >使用状态：</span
                    >
                    <dictionary
                        :statusOptions="modelStatusOptions"
                        :value="detail?.useStations?.length > 0"
                        :color="'color'"
                    />
                </div>
                <div class="py-1 flex">
                    <span
                        class="title-l title-content text-secondar-text dark:text-60-dark"
                        >使用情况：</span
                    >
                    <div class="title-r col flex-1 w-0">
                        <div
                            v-for="(item, index) in detail?.useStations"
                            :key="index"
                        >
                            {{ item?.stationName }}-{{
                                item.effectiveMonths.join('/')
                            }}
                        </div>
                        <div class="" v-if="detail?.useStations?.length === 0">
                            -
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <a-divider class="my-4"></a-divider>
        <div>
            <div class="price-chart mb-4">
                <div class="head">
                    <div class="text-primary-text dark:text-80-dark">
                        配置预览：
                    </div>
                    <div class="legend">
                        <!-- <div class="mark charge"></div>
                        <span>充电</span>
                        <div class="mark uncharge"></div>
                        <span>放电</span> -->
                    </div>
                </div>
                <canvas
                    style="width: 440px; height: 240px; z-index: 1"
                    width="440"
                    height="240"
                    canvas-id="priceChart"
                    id="priceChart"
                    ref="priceChart"
                ></canvas>
            </div>
            <div class="">
                <div
                    class="flex justify-between items-center py-2 px-3 bg-background rounded-lg price-flex text-secondar-text dark:text-60-dark"
                >
                    <div class="hours">起止时间段</div>
                    <div class="hours-type">时段类型</div>
                    <div class="price">
                        买入价格<span class="text-xs">(元/kWh)</span>
                    </div>
                    <div class="price">
                        卖出价格<span class="text-xs">(元/kWh)</span>
                    </div>
                </div>
                <div
                    class="flex justify-between items-center py-1 px-3 price-flex text-primary-text dark:text-80-dark"
                    v-for="(item, index) in workProcessList"
                    :key="index"
                >
                    <div class="hours">
                        {{ item.startTime }}-{{ item.endTime }}
                    </div>
                    <div class="hours-type">
                        <dictionary
                            :statusOptions="segmentTypes"
                            :value="item.segmentType"
                        />
                    </div>
                    <div class="price">{{ item.price }}</div>
                    <div class="price">{{ item.salePrice }}</div>
                </div>
            </div>
        </div>
        <add-model
            v-model:visible="addVisible"
            v-model:detail="detail"
            :isEdit="true"
            @onClose="onModalClose"
            @update="onUpdate"
            class="relative"
            style="position: relative; z-index: 2016"
        />
    </el-drawer>
</template>

<script>
import { ref, reactive, toRaw, watch, nextTick, toRefs } from 'vue'
import { DrawCanvas, formatData } from '@/common/drawPriceChart'
import { fullTimePeriod, modelStatusOptions, segmentTypes } from '../util'
import Dictionary from '@/components/table/dictionary.vue'
import addModel from './components/addModel.vue'
import api from '@/apiService/strategy'

export default {
    components: { Dictionary, addModel },
    data() {
        return {
            workProcessList: [],
            detail: {},
            usingInfo: {},
        }
    },
    watch: {
        visible: {
            deep: true,
            handler(val) {
                if (val) {
                    this.getData()
                }
            },
        },
    },
    methods: {
        drawPrice(workProcessGroup, prices) {
            let data = formatData(workProcessGroup, prices)
            var context = document.getElementById('priceChart').getContext('2d')
            var painter = new DrawCanvas(context)
            painter.update(data)
            painter.clear()
            painter.paint()
        },
        async getData() {
            //
            let res = await api.getElecPriceTemplateDetail({
                id: this.selectedTempId,
                stationId: this.$route.query.stationId,
            })
            let detail = res.data.data
            this.detail = res.data.data
            let workProcessList = detail.electricSegments.map((item) => {
                return {
                    ...item,
                    startHour: parseInt(item.startTime.substring(0, 2), 10),
                    endHour: parseInt(item.endTime.substring(0, 2), 10),
                    segmentType: item.segmentType,
                    batteryWorkStatus: 0,
                    price: item.price,
                    salePrice: item.salePrice,
                }
            })
            let workProcessGroup = {
                season: detail.name,
                workProcessId: null,
                workProcessList: workProcessList,
            }
            this.workProcessList = workProcessList
            this.usingInfo = detail.useStations.map((item) => {
                return {}
            })
            nextTick(() => {
                this.drawPrice(workProcessGroup, { ...detail })
            })
        },
        async onDelete() {
            await api.deleteElecPriceTemplate({
                id: this.selectedTempId,
                stationId: this.$route.query.stationId,
            })
            //
            this.onClose()
            this.$emit('update')
        },
        async onUpdate() {
            await this.getData()
            this.$emit('update')
        },
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        selectedTempId: {
            type: String,
            default: null,
        },
    },
    setup(props, { emit }) {
        const { visible } = toRefs(props)
        const priceChart = ref()
        const cancel = () => {}
        const onClose = () => {
            // var context = document.getElementById('priceChart').getContext('2d')
            // var painter = new DrawCanvas(context)
            // // painter.update(data)
            // painter.clear()
            emit('onClose')
        }

        const addVisible = ref(false)
        const onEmit = () => {
            //
            addVisible.value = true
        }
        const onModalClose = () => {
            addVisible.value = false
        }
        return {
            priceChart,
            cancel,
            onClose,
            onEmit,
            addVisible,
            modelStatusOptions,
            segmentTypes,
            onModalClose,
        }
    },
}
</script>

<style lang="less" scoped>
.price-chart {
    .head {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .legend {
            display: flex;
            align-items: center;
            font-size: 14px;

            .mark {
                width: 20px;
                height: 8px;
                border-radius: 5px;
                margin-right: 5px;

                &.charge {
                    background: rgba(111, 190, 206, 0.5);
                }

                &.uncharge {
                    margin-left: 14px;
                    background: rgba(253, 117, 11, 0.2);
                }
            }
        }
    }
}

.price-flex {
    text-align: center;

    .hours {
        width: 84px;
        text-align: left;
    }

    .hours-type {
        width: 58px;
    }

    .price {
        width: 108px;
    }
}

:deep(
        .ant-btn-primary[disabled],
        .ant-btn-primary[disabled]:hover,
        .ant-btn-primary[disabled]:focus,
        .ant-btn-primary[disabled]:active
    ) {
    color: #fff;
    background-color: var(--themeColor);
    border-color: var(--themeColor);
    opacity: 0.6;
}
</style>
<style lang="less">
// .ant-popover {
//     z-index: 1060;
// }
</style>
