<template>
    <el-button plain round class="ml-4 btn-hover" :disabled="disabled">
        <span>{{ t('Export_daochu') }}</span>
        <span class="icon-box ml-0.5" style="width: 20px">
            <iconSvg name="export" class="icon-default" />
        </span>
    </el-button>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
const props = defineProps({
    disabled: {
        type: Boolean,
        default: false,
    },
})
</script>

<style lang="scss" scoped></style>
